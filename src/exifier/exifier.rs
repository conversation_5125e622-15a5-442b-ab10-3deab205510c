use std::sync::Arc;

use crate::exifier::grammar::EXIGrammar;
use crate::exifier::prelude::*;
use crate::exifier::writer::BitStreamWriter;
use quick_xml::events::Event;
use quick_xml::reader::Reader;
use serde_json::Value;
use xml2json_rs::XmlConfig;

#[derive(Debug)]
pub struct Exifier {
    grammar: EXIGrammar,
    xml: String,
    bitstream_writer: BitStreamWriter,
    current_grammar_id: u32,
    grammar_stack: Vec<u32>,
}

impl Exifier {
    pub fn default() -> Self {
        Exifier {
            grammar: EXIGrammar::default(),
            xml: String::new(),
            bitstream_writer: BitStreamWriter::new(),
            current_grammar_id: 0,
            grammar_stack: Vec::new(),
        }
    }

    pub fn load_grammar(&mut self, exi_grammar: EXIGrammar) -> Result<()> {
        tracing::debug!(
            "Loading EXI grammar with {} grammar rules",
            exi_grammar.grs.grammar.len()
        );
        tracing::debug!(
            "Document grammar ID: {}",
            exi_grammar.grs.document_grammar_id
        );
        tracing::debug!(
            "Fragment grammar ID: {}",
            exi_grammar.grs.fragment_grammar_id
        );
        // TODO: Store the grammar for use in encoding/decoding
        self.grammar = exi_grammar;
        Ok(())
    }

    pub fn encode(&mut self, json_value: Value) -> Result<Vec<u8>> {
        tracing::debug!("Encode JSON value: {}", json_value);

        // Convert JSON value to string for XML conversion
        let json_string = serde_json::to_string(&json_value).unwrap();
        self.prepare_encoder(&json_string);
        self.encode_header()?;
        self.encode_body()?;

        tracing::debug!("Result so far: {:02X?}", self.bitstream_writer.into_bytes());
        // Return the incoming JSON as a vector of bytes
        Ok(json_string.into_bytes())
    }

    pub fn decode(&self, encoded_bytes: Vec<u8>) -> Result<Value> {
        // Decode the incoming bytes to a string
        let json_string = String::from_utf8(encoded_bytes).unwrap();
        tracing::debug!("Decoded bytes to JSON string: {}", json_string);

        // Parse the JSON string into a Value
        let json_value: Value = serde_json::from_str(&json_string)
            .map_err(|e| crate::exifier::Error::Generic(format!("Failed to parse JSON: {}", e)))?;

        tracing::debug!("Parsed JSON value: {}", json_value);
        Ok(json_value)
    }

    fn parse_xml(&mut self) {
        // Clone the XML string to avoid borrowing issues
        let xml_content = self.xml.clone();
        let mut reader = Reader::from_str(&xml_content);
        self.current_grammar_id = 1;
        let mut buf = Vec::new();
        loop {
            tracing::debug!("Current grammar id: {:?}", self.current_grammar_id);
            //tracing::debug!("Current grammar stack: {:?}", self.grammar_stack);
            match reader.read_event_into(&mut buf) {
                Ok(Event::Start(e)) => {
                    tracing::debug!("Start element: {:?}", e.name());
                    self.grammar_stack.push(self.current_grammar_id);
                    let _ = self.encode_event(&Event::Start(e));
                }
                Ok(Event::Text(e)) => {
                    tracing::debug!("Text: {:?}", e.unescape().unwrap());
                    let _ = self.encode_event(&Event::Text(e));
                }
                Ok(Event::End(e)) => {
                    let next_grammar_id = self.grammar_stack.pop();
                    if let Some(grammar_id) = next_grammar_id {
                        self.current_grammar_id = grammar_id;
                    }
                    tracing::debug!("End element: {:?} {:#?}", e.name(), self.current_grammar_id);
                    let _ = self.encode_event(&Event::End(e));
                }
                Ok(Event::Eof) => break,
                Err(e) => panic!("Error: {:?}", e),
                Ok(Event::Empty(e)) => {
                    tracing::debug!("Empty element: {:?}", e.name());
                }
                Ok(Event::Decl(e)) => {
                    tracing::debug!("Declaration. {:#?}", e);
                }
                _ => {}
            }
            buf.clear();
        }

        tracing::debug!(
            "Parse completed. Final grammar stack: {:?}",
            self.grammar_stack
        );
    }

    fn encode_event(&mut self, event: &Event) -> Result<()> {
        match event {
            Event::Start(e) => {
                let current_rule = self
                    .grammar
                    .grs
                    .grammar
                    .iter()
                    .find(|g| g.grammar_id == self.current_grammar_id.to_string());
                // let matching_production = current_rule.production.iter().find(|p| p.event == "startElement" && self.grammar.qnames.namespace_context.get(p.start_element_namespace_id.unwrap()).qnames_context.find(|q| q.local_name_id == p.start_element_local_name_id.unwrap()).is_some());

                if let Some(rule) = current_rule {
                    for (index, production) in rule.production.iter().enumerate() {
                        if production.event == "startElement" {
                            if let Some(namespace_id) = production.start_element_namespace_id {
                                if let Some(namespace) = self
                                    .grammar
                                    .qnames
                                    .namespace_context
                                    .get(namespace_id as usize)
                                {
                                    if let Some(local_name_id) =
                                        production.start_element_local_name_id
                                    {
                                        if let Some(local_name) = namespace
                                            .qname_context
                                            .iter()
                                            .find(|q| q.local_name_id == local_name_id)
                                        {
                                            if local_name.local_name.as_bytes() == e.name().as_ref()
                                            {
                                                self.current_grammar_id =
                                                    production.start_element_grammar_id.unwrap()
                                                        as u32;
                                                tracing::debug!(
                                                    "Matched start element: {}. Index: {}/{}",
                                                    local_name.local_name,
                                                    index,
                                                    rule.production.len()
                                                );
                                                let bits_to_use =
                                                    (rule.production.len() as f32).log2().ceil()
                                                        as u8;
                                                self.bitstream_writer
                                                    .write_n_bit_data(index as u8, bits_to_use);
                                                return Ok(());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            Event::Text(e) => {
                let current_rule = self
                    .grammar
                    .grs
                    .grammar
                    .iter()
                    .find(|g| g.grammar_id == self.current_grammar_id.to_string());
                if let Some(rule) = current_rule {
                    // tracing::debug!(
                    //     "Number of productions: {}\n{:#?}",
                    //     rule.production.len(),
                    //     rule.production
                    // );
                    for production in &rule.production {
                        if &production.event == "startElement" {
                            // This is text. We need to know what type it is before encoding.
                            // The start element points at the element type.

                            let qname_context = self.grammar.qnames.namespace_context
                                [production.start_element_namespace_id.unwrap() as usize]
                                .qname_context
                                .iter()
                                .find(|n| {
                                    n.uri_id == production.start_element_namespace_id.unwrap()
                                        && n.local_name_id
                                            == production.start_element_local_name_id.unwrap()
                                })
                                .unwrap();
                            let element_type = qname_context.local_name.clone();
                            tracing::debug!("Element type is {:#?}", element_type);

                            // We are expecting next grammar rule to have one production - and just characters.
                            let next_grammar_rule = self
                                .grammar
                                .grs
                                .grammar
                                .iter()
                                .find(|g| {
                                    g.grammar_id
                                        == production.start_element_grammar_id.unwrap().to_string()
                                })
                                .unwrap();
                            let next_grammar_prods = &next_grammar_rule.production;
                           // tracing::debug!("Next element productions: {:#?}", next_grammar_prods);

                            if element_type == "string" {
                                // Write the text
                                let text = e.unescape().unwrap();
                                tracing::debug!("Writing text: {}", text);
                            } else if element_type == "number" {
                                let text = e.unescape().unwrap();
                                tracing::debug!("Writing number: {}", text);

                                let mut int_val: i128 = text.parse::<i128>().unwrap_or(0);

                                if int_val >= 0 {
                                    self.bitstream_writer.write_n_bit_data(0, 1);
                                } else {
                                    self.bitstream_writer.write_n_bit_data(1, 1);
                                    int_val = int_val.abs() - 1; // Spec says non-negative values are stored as value - 1.
                                }

                                //self.write_unsigned_int(int_val as u128);

                                {
                                    let mut data = int_val as u128;
                                    if data < 128 {
                                        self.bitstream_writer.write_n_bit_data(data as u8, 8);
                                    } else {
                                        let mut blocks_required = 1;

                                        while blocks_required > 1 {
                                            self.bitstream_writer
                                                .write_n_bit_data(((data & 0x7F) | 0x80) as u8, 8);
                                            data >>= 7;
                                            blocks_required -= 1;
                                        }

                                        self.bitstream_writer.write_n_bit_data(data as u8, 8);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            Event::End(_e) => {
                tracing::debug!("End element: {:?}", _e.name());
                let current_rule = self
                    .grammar
                    .grs
                    .grammar
                    .iter()
                    .find(|g| g.grammar_id == self.current_grammar_id.to_string());
                // let matching_production = current_rule.production.iter().find(|p| p.event == "startElement" && self.grammar.qnames.namespace_context.get(p.start_element_namespace_id.unwrap()).qnames_context.find(|q| q.local_name_id == p.start_element_local_name_id.unwrap()).is_some());

                if let Some(rule) = current_rule {
                    for (index, production) in rule.production.iter().enumerate() {
                        if production.event == "endElement" {
                            tracing::debug!(
                                "Matched end element. Index: {}/{}",
                                index,
                                rule.production.len()
                            );
                            let bits_to_use = (rule.production.len() as f32).log2().ceil() as u8;
                            self.bitstream_writer
                                .write_n_bit_data(index as u8, bits_to_use);
                            return Ok(());
                        }
                    }
                }
            }
            _ => {}
        }
        Ok(())
    }

    fn write_unsigned_int(&mut self, value: u128) {
        let mut data = value;
        if data < 128 {
            self.bitstream_writer.write_n_bit_data(data as u8, 8);
        } else {
            let mut blocks_required = 1;

            while blocks_required > 1 {
                self.bitstream_writer
                    .write_n_bit_data(((data & 0x7F) | 0x80) as u8, 8);
                data >>= 7;
                blocks_required -= 1;
            }

            self.bitstream_writer.write_n_bit_data(data as u8, 8);
        }
    }

    fn prepare_encoder(&mut self, json_string: &str) {
        let mut xml_builder = XmlConfig::new().root_name(String::from("map")).finalize();
        self.xml = xml_builder.build_from_json_string(&json_string).unwrap();
        tracing::debug!("Generated XML: {}", &self.xml);
    }

    fn encode_header(&mut self) -> Result<()> {
        self.bitstream_writer.write_n_bit_data(2, 2);
        // // Options bit 0 (1 bit)
        self.bitstream_writer.write_n_bit_data(0, 1);
        // // Header version 00000 (5 bits)
        self.bitstream_writer.write_n_bit_data(0, 5);

        tracing::debug!("Encoded header.");
        Ok(())
    }

    fn encode_body(&mut self) -> Result<()> {
        self.parse_xml();
        Ok(())
    }
}
