const BITS_IN_BYTE: u8 = 8;

#[derive(Debug)]
pub struct BitStreamWriter {
    buffer: Vec<u8>,
    /// Next position of 0 means the last byte is full or we are on the first byte. Next data will be written on a new byte.
    next_bit_position: u8,
}

impl BitStreamWriter {
    pub fn new() -> Self {
        BitStreamWriter {
            buffer: Vec::new(),
            next_bit_position: 0,
        }
    }

    pub fn write_n_bit_data(&mut self, data: u8, num_bits: u8) {
        if num_bits == 0 {
            tracing::warn!("Tried to write {} with 0 bits", data);
            return;
        }

        if num_bits > BITS_IN_BYTE {
            tracing::warn!("Can't write more than a byte with write_n_bit_data. Tried to write {} with {} bits", data, num_bits);
            return;
        }

        if self.next_bit_position + num_bits <= BITS_IN_BYTE {
            self.write_bits_to_stream(data, num_bits);
        } else {
            let num_bits_first_byte = BITS_IN_BYTE - self.next_bit_position;
            let num_bits_second_byte = num_bits - num_bits_first_byte;

            let data_in_first_byte = data >> num_bits_second_byte;
            self.write_bits_to_stream(data_in_first_byte, num_bits_first_byte);

            let data_in_second_byte = (data << (BITS_IN_BYTE - num_bits_first_byte))
                >> (BITS_IN_BYTE - num_bits_first_byte);
            self.write_bits_to_stream(data_in_second_byte, num_bits_second_byte);
        }
        tracing::debug!("Wrote {}({} bits)", data, num_bits);
        tracing::debug!("Buffer so far: {:02X?}", self.buffer);
    }

    pub fn write_bits_to_stream(&mut self, data: u8, num_bits: u8) {
        if self.next_bit_position == 0 {
            self.buffer.push(0);
        }
        let mut next_byte = self.buffer.pop().unwrap_or(0u8);

        let mask = ((1u16 << num_bits) - 1) as u8;

        let left_aligned = (data & mask) << (8 - (self.next_bit_position + num_bits));
        next_byte |= left_aligned;
        self.buffer.push(next_byte);

        self.next_bit_position += num_bits;
        if self.next_bit_position >= BITS_IN_BYTE {
            self.next_bit_position = 0;
        }
    }

    pub fn into_bytes(&self) -> Vec<u8> {
        self.buffer.clone()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_new_bitstream_writer() {
        let writer = BitStreamWriter::new();
        assert_eq!(writer.buffer.len(), 0);
        assert_eq!(writer.next_bit_position, 0);
        assert_eq!(writer.into_bytes(), Vec::<u8>::new());
    }

    #[test]
    fn test_write_single_bit() {
        let mut writer = BitStreamWriter::new();

        // Write a single 1 bit
        writer.write_n_bit_data(1, 1);
        assert_eq!(writer.buffer, vec![0b10000000]);
        assert_eq!(writer.next_bit_position, 1);

        // Write another 1 bit
        writer.write_n_bit_data(1, 1);
        assert_eq!(writer.buffer, vec![0b11000000]);
        assert_eq!(writer.next_bit_position, 2);

        // Write a 0 bit
        writer.write_n_bit_data(0, 1);
        assert_eq!(writer.buffer, vec![0b11000000]);
        assert_eq!(writer.next_bit_position, 3);
    }

    #[test]
    fn test_write_multiple_bits() {
        let mut writer = BitStreamWriter::new();

        // Write 3 bits: 101
        writer.write_n_bit_data(0b101, 3);
        assert_eq!(writer.buffer, vec![0b10100000]);
        assert_eq!(writer.next_bit_position, 3);

        // Write 2 more bits: 11
        writer.write_n_bit_data(0b11, 2);
        assert_eq!(writer.buffer, vec![0b10111000]);
        assert_eq!(writer.next_bit_position, 5);
    }

    #[test]
    fn test_write_full_byte() {
        let mut writer = BitStreamWriter::new();

        // Write a full byte
        writer.write_n_bit_data(0b11010110, 8);
        assert_eq!(writer.buffer, vec![0b11010110]);
        assert_eq!(writer.next_bit_position, 0);
    }

    #[test]
    fn test_write_across_byte_boundary() {
        let mut writer = BitStreamWriter::new();

        // Fill first byte with 6 bits
        writer.write_n_bit_data(0b111010, 6);
        assert_eq!(writer.buffer, vec![0b11101000]);
        assert_eq!(writer.next_bit_position, 6);

        // Write 4 more bits, should span to next byte
        writer.write_n_bit_data(0b1101, 4);
        assert_eq!(writer.buffer, vec![0b11101011, 0b01000000]);
        assert_eq!(writer.next_bit_position, 2);
    }

    #[test]
    fn test_write_exactly_byte_boundary() {
        let mut writer = BitStreamWriter::new();

        // Write 5 bits
        writer.write_n_bit_data(0b10110, 5);
        assert_eq!(writer.buffer, vec![0b10110000]);
        assert_eq!(writer.next_bit_position, 5);

        // Write exactly 3 more bits to complete the byte
        writer.write_n_bit_data(0b101, 3);
        assert_eq!(writer.buffer, vec![0b10110101]);
        assert_eq!(writer.next_bit_position, 0);

        // Write one more bit, should start new byte
        writer.write_n_bit_data(1, 1);
        assert_eq!(writer.buffer, vec![0b10110101, 0b10000000]);
        assert_eq!(writer.next_bit_position, 1);
    }

    #[test]
    fn test_write_zero_bits() {
        let mut writer = BitStreamWriter::new();

        // Writing 0 bits should do nothing
        writer.write_n_bit_data(0b11111111, 0);
        assert_eq!(writer.buffer.len(), 0);
        assert_eq!(writer.next_bit_position, 0);
    }

    #[test]
    fn test_write_data_with_extra_bits() {
        let mut writer = BitStreamWriter::new();

        // Write only 3 bits of 0b11110101, should mask to 0b101
        writer.write_n_bit_data(0b11110101, 3);
        assert_eq!(writer.buffer, vec![0b10100000]);
        assert_eq!(writer.next_bit_position, 3);
    }

    #[test]
    fn test_multiple_bytes() {
        let mut writer = BitStreamWriter::new();

        // Write pattern that spans multiple bytes
        writer.write_n_bit_data(0b11111111, 8); // First byte: 11111111
        writer.write_n_bit_data(0b1010, 4); // Second byte: 10100000
        writer.write_n_bit_data(0b11, 2); // Second byte: 10111000
        writer.write_n_bit_data(0b01, 2); // Second byte: 10110100

        assert_eq!(writer.buffer, vec![0b11111111, 0b10101101]);
        assert_eq!(writer.next_bit_position, 0);
    }

    #[test]
    fn test_into_bytes_preserves_data() {
        let mut writer = BitStreamWriter::new();

        writer.write_n_bit_data(0b10101010, 8);
        writer.write_n_bit_data(0b1100, 4);

        let bytes1 = writer.into_bytes();
        let bytes2 = writer.into_bytes();

        // Should return the same data each time
        assert_eq!(bytes1, bytes2);
        assert_eq!(bytes1, vec![0b10101010, 0b11000000]);
    }

    #[test]
    fn test_write_8_bits_with_mask() {
        let mut writer = BitStreamWriter::new();

        // Test that writing 8 bits works correctly (this was causing overflow)
        writer.write_n_bit_data(0xFF, 8);
        assert_eq!(writer.buffer, vec![0xFF]);
        assert_eq!(writer.next_bit_position, 0);

        // Test with another 8-bit value
        let mut writer2 = BitStreamWriter::new();
        writer2.write_n_bit_data(0b10101010, 8);
        assert_eq!(writer2.buffer, vec![0b10101010]);
    }

    #[test]
    fn test_error_handling_too_many_bits() {
        let mut writer = BitStreamWriter::new();

        // Try to write more than 8 bits - should be handled gracefully
        writer.write_n_bit_data(0b11111111, 9);

        // Buffer should remain empty since the operation should fail
        assert_eq!(writer.buffer.len(), 0);
        assert_eq!(writer.next_bit_position, 0);
    }
}
