#![allow(unused)]
use std::fs;
use std::path::PathBuf;

use clap::Parser;
use serde_json::Value;

// To be removed later.
use tracing_subscriber;
mod exifier;

use exifier::{E<PERSON><PERSON><PERSON>mar, Error, Exifier};

/// Compare two JSON values semantically, ignoring element order
fn compare_json_values(value1: &Value, value2: &Value) -> bool {
    // Direct comparison of JSON values (this handles order independence)
    value1 == value2
}

/// Compare a JSON string with a JSON value semantically, ignoring element order
fn compare_json_string_with_value(json_str: &str, json_value: &Value) -> Result<bool, String> {
    // Parse the JSON string
    let parsed_value: Value = serde_json::from_str(json_str)
        .map_err(|e| format!("Failed to parse JSON string: {}", e))?;

    // Compare the parsed values
    Ok(compare_json_values(&parsed_value, json_value))
}

#[derive(Parse<PERSON>, Debug)]
#[command(version, about, long_about = None)]
struct Args {
    /// Name of the person to greet
    #[arg(
        short,
        long,
        help = "Json to encode to exi",
        default_value = r#"{"lastname": "Smith","age": 30}"#
    )]
    json: String,
    #[arg(
        short,
        long,
        help = "Path to grammar file",
        default_value = r#"./../../resources/examples/example_1/test_schema.xsd.grs"#
    )]
    grammar: String,
    #[arg(short, long, help = "Exi to decode", default_value = r#""#)]
    exi: String,
}

fn main() {
    tracing_subscriber::fmt::init();
    let args = Args::parse();
    let json_to_encode = args.json;
    tracing::info!("Json to encode:\n {json_to_encode}");

    // Read grammar content from file path
    let grammar_path = args.grammar;
    tracing::info!("Grammar file path: {grammar_path}");
    let grammar_content = fs::read_to_string(&grammar_path).expect(&format!(
        "Error reading grammar file from path: {}",
        grammar_path
    ));
    tracing::info!("Grammar content loaded from file.");

    // Parse the GRS file
    let exi_grammar = EXIGrammar::from_json(&grammar_content).expect("Failed to parse GRS file");
    tracing::info!("GRS file read.");
    tracing::debug!(
        "Document grammar ID: {}",
        exi_grammar.grs.document_grammar_id
    );
    tracing::debug!(
        "Fragment grammar ID: {}",
        exi_grammar.grs.fragment_grammar_id
    );

    let mut exifier = Exifier::default();
    exifier.load_grammar(exi_grammar);

    // Parse JSON string to Value for encoding
    let json_value: Value =
        serde_json::from_str(&json_to_encode).expect("Failed to parse JSON string");
    let encoded_bytes = exifier.encode(json_value).unwrap();
    tracing::info!("Encoded exi:");
    let hex = encoded_bytes
        .iter()
        .map(|b| format!("{:02X}", b))
        .collect::<Vec<_>>()
        .join(" ");
    tracing::info!("{}", hex);
    let decoded_json = exifier.decode(encoded_bytes).unwrap();
    tracing::info!("Decoded exi: {decoded_json}");

    // Compare the original and decoded JSON
    let comparison_result = compare_json_string_with_value(&json_to_encode, &decoded_json);
    match comparison_result {
        Ok(are_equal) => {
            if are_equal {
                tracing::info!(
                    "✅ JSON comparison: Original and decoded JSON are semantically identical"
                );
            } else {
                tracing::warn!("❌ JSON comparison: Original and decoded JSON are different");
            }
        }
        Err(e) => {
            tracing::error!("❌ JSON comparison failed: {}", e);
        }
    }
}

fn load_grammar_from_file() -> String {
    fs::read_to_string("/Users/<USER>/Desktop/Personal/repos/exify-codec/resources/examples/example_1/test_schema.json").expect("Error reading example json from disk.")
}
