from setuptools import setup
from pyo3_setuptools_rust import Binding, RustExtension

setup(
    name="exify",
    version="0.1.0",
    description="EXI (Efficient XML Interchange) codec with Python bindings",
    author="Your Name",
    author_email="<EMAIL>",
    rust_extensions=[
        RustExtension(
            "exify.exify",
            binding=Binding.PyO3,
            path="Cargo.toml",
        )
    ],
    packages=["exify"],
    zip_safe=False,
    python_requires=">=3.7",
    install_requires=[],
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Rust",
    ],
)
