[build-system]
requires = ["maturin>=1.0,<2.0"]
build-backend = "maturin"

[project]
name = "exify"
version = "0.1.0"
description = "EXI (Efficient XML Interchange) codec with Python bindings"
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.7"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Rust",
]

[tool.maturin]
features = ["pyo3/extension-module"]
